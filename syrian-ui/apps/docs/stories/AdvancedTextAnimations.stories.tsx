/**
 * Syrian Identity Advanced Text Animation Stories
 * 
 * Showcasing sophisticated text animations with Syrian cultural elements and GSAP-inspired techniques.
 */

import type { Meta, StoryObj } from '@storybook/react';
import { 
  AnimationProvider,
  SyrianSplitText,
  CulturalTextReveal
} from '@sid/components';

// Main meta export for Storybook
const meta: Meta<typeof SyrianSplitText> = {
  title: 'Animations/Text/Advanced Text Animations',
  component: SyrianSplitText,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
The Advanced Text Animation components provide sophisticated text reveal animations inspired by Syrian culture and Islamic art.

**Cultural Context:**
- **SyrianSplitText**: Character-by-character or word-by-word animations with cultural timing
- **CulturalTextReveal**: Pattern-based reveals inspired by Syrian design elements
- **RTL Support**: Full Arabic text support with proper directional animations
- **Cultural Timing**: Based on Syrian musical rhythms and contemplative pacing

**Features:**
- Multiple animation styles (cultural, calligraphy, geometric, Damascus)
- Syrian color schemes (heritage, flag, forest, wheat, Damascus)
- Performance optimized with accessibility compliance
- Scroll-triggered animations with intersection observer
- Cultural typography variants
        `
      }
    }
  },
  decorators: [
    (Story) => (
      <AnimationProvider>
        <div style={{ 
          padding: '3rem',
          backgroundColor: 'var(--sid-charcoal-50)',
          borderRadius: '12px',
          minHeight: '300px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          textAlign: 'center'
        }}>
          <Story />
        </div>
      </AnimationProvider>
    ),
  ],
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Free Syria - Cultural Split Text
export const FreeSyriaCultural: Story = {
  args: {
    text: "سوريا حُرّة",
    animationStyle: 'cultural',
    timing: 'ceremonial',
    splitType: 'chars',
    staggerDelay: 100,
    direction: 'rtl',
    colorScheme: 'flag',
    variant: 'heading',
  },
  parameters: {
    docs: {
      description: {
        story: 'Cultural character-by-character animation of "Free Syria" in Arabic with Syrian flag colors and ceremonial timing.'
      }
    }
  }
};

// Free Syria English - Damascus Style
export const FreeSyriaEnglish: Story = {
  args: {
    text: "Free Syria",
    animationStyle: 'damascus',
    timing: 'contemplative',
    splitType: 'chars',
    staggerDelay: 80,
    direction: 'ltr',
    colorScheme: 'damascus',
    variant: 'heading',
  },
  parameters: {
    docs: {
      description: {
        story: 'Damascus-style character animation of "Free Syria" in English with contemplative timing and Damascus color scheme.'
      }
    }
  }
};

// Bilingual Message - Calligraphy Style
export const BilingualMessage: Story = {
  args: {
    text: "سوريا حُرّة - Free Syria",
    animationStyle: 'calligraphy',
    timing: 'contemplative',
    splitType: 'words',
    staggerDelay: 200,
    direction: 'auto',
    colorScheme: 'heritage',
    variant: 'subheading',
  },
  parameters: {
    docs: {
      description: {
        story: 'Bilingual message with calligraphy-inspired word-by-word animation, automatically detecting text direction.'
      }
    }
  }
};

// Geometric Pattern Animation
export const GeometricPattern: Story = {
  args: {
    text: "الجمهورية العربية السورية",
    animationStyle: 'geometric',
    timing: 'ceremonial',
    splitType: 'words',
    staggerDelay: 150,
    direction: 'rtl',
    colorScheme: 'forest',
    variant: 'body',
  },
  parameters: {
    docs: {
      description: {
        story: 'Geometric pattern animation for "Syrian Arab Republic" with forest color scheme and ceremonial timing.'
      }
    }
  }
};

// Cultural Text Reveal Examples
export const CulturalRevealFreedom: Story = {
  render: () => (
    <AnimationProvider>
      <div style={{ 
        padding: '3rem',
        backgroundColor: 'var(--sid-charcoal-50)',
        borderRadius: '12px',
        minHeight: '300px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <CulturalTextReveal 
          text="سوريا حُرّة - Free Syria"
          pattern="bloom"
          theme="freedom"
          intensity="dramatic"
          direction="auto"
        />
      </div>
    </AnimationProvider>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Cultural text reveal with bloom pattern, freedom theme, and dramatic intensity for the bilingual "Free Syria" message.'
      }
    }
  }
};

// Heritage Wave Pattern
export const HeritageWave: Story = {
  render: () => (
    <AnimationProvider>
      <div style={{ 
        padding: '3rem',
        backgroundColor: 'var(--sid-forest-50)',
        borderRadius: '12px',
        minHeight: '300px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <CulturalTextReveal 
          text="التراث السوري العريق"
          pattern="wave"
          theme="heritage"
          intensity="normal"
          direction="rtl"
        />
      </div>
    </AnimationProvider>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Wave pattern reveal for "Ancient Syrian Heritage" with heritage theme and normal intensity.'
      }
    }
  }
};

// Damascus Pattern Reveal
export const DamascusReveal: Story = {
  render: () => (
    <AnimationProvider>
      <div style={{ 
        padding: '3rem',
        backgroundColor: 'var(--sid-charcoal-100)',
        borderRadius: '12px',
        minHeight: '300px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <CulturalTextReveal 
          text="دمشق عاصمة الثقافة"
          pattern="damascus"
          theme="strength"
          intensity="dramatic"
          direction="rtl"
        />
      </div>
    </AnimationProvider>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Damascus pattern reveal for "Damascus, Capital of Culture" with strength theme and dramatic intensity.'
      }
    }
  }
};

// Peace and Unity Theme
export const PeaceUnity: Story = {
  render: () => (
    <AnimationProvider>
      <div style={{ 
        padding: '3rem',
        backgroundColor: 'var(--sid-wheat-50)',
        borderRadius: '12px',
        minHeight: '300px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <CulturalTextReveal 
          text="السلام والوحدة - Peace and Unity"
          pattern="spiral"
          theme="peace"
          intensity="subtle"
          direction="auto"
        />
      </div>
    </AnimationProvider>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Spiral pattern reveal for "Peace and Unity" message with peace theme and subtle intensity.'
      }
    }
  }
};

// Performance Comparison
export const PerformanceShowcase: Story = {
  render: () => (
    <AnimationProvider>
      <div style={{ 
        padding: '2rem',
        backgroundColor: 'var(--sid-charcoal-50)',
        borderRadius: '12px',
        display: 'flex',
        flexDirection: 'column',
        gap: '2rem',
        alignItems: 'center'
      }}>
        <div style={{ textAlign: 'center' }}>
          <h3 style={{ marginBottom: '1rem', color: 'var(--sid-charcoal-900)' }}>
            مقارنة الأداء - Performance Comparison
          </h3>
          <p style={{ color: 'var(--sid-charcoal-600)', marginBottom: '2rem' }}>
            Different animation styles with varying performance characteristics
          </p>
        </div>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem', width: '100%', maxWidth: '800px' }}>
          <SyrianSplitText 
            text="أداء سريع - Fast Performance"
            animationStyle="default"
            timing="fast"
            splitType="words"
            colorScheme="forest"
            variant="body"
          />
          
          <SyrianSplitText 
            text="أداء متوازن - Balanced Performance"
            animationStyle="cultural"
            timing="normal"
            splitType="chars"
            colorScheme="heritage"
            variant="body"
          />
          
          <SyrianSplitText 
            text="جودة عالية - High Quality"
            animationStyle="damascus"
            timing="contemplative"
            splitType="chars"
            colorScheme="damascus"
            variant="body"
          />
        </div>
      </div>
    </AnimationProvider>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Performance comparison showing different animation styles with varying complexity and performance characteristics.'
      }
    }
  }
};

// Accessibility Features
export const AccessibilityDemo: Story = {
  render: () => (
    <AnimationProvider>
      <div style={{ 
        padding: '2rem',
        backgroundColor: 'var(--sid-charcoal-50)',
        borderRadius: '12px',
        textAlign: 'center'
      }}>
        <div style={{ marginBottom: '2rem' }}>
          <h3 style={{ color: 'var(--sid-charcoal-900)', marginBottom: '1rem' }}>
            إمكانية الوصول - Accessibility Features
          </h3>
          <p style={{ color: 'var(--sid-charcoal-600)' }}>
            Animations automatically respect user preferences for reduced motion
          </p>
        </div>
        
        <SyrianSplitText 
          text="نص يحترم تفضيلات المستخدم"
          animationStyle="cultural"
          timing="contemplative"
          splitType="chars"
          colorScheme="heritage"
          variant="subheading"
        />
        
        <div style={{ marginTop: '1rem', fontSize: '0.875rem', color: 'var(--sid-charcoal-500)' }}>
          <p>Try enabling "Reduce motion" in your system preferences to see the fallback behavior</p>
        </div>
      </div>
    </AnimationProvider>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Accessibility demonstration showing how animations respect user preferences for reduced motion.'
      }
    }
  }
};
