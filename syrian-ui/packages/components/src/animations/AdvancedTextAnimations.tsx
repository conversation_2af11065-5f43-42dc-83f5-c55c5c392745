/**
 * Syrian Identity Advanced Text Animations
 * 
 * Sophisticated text animations with GSAP integration and Syrian cultural elements:
 * - SplitText with Arabic/English support and cultural timing
 * - Cultural reveal animations with Syrian color schemes
 * - Advanced typography animations with Islamic art inspiration
 * - Performance-optimized with accessibility compliance
 */

import * as React from 'react';
import { useAnimation } from './AnimationProvider';

// Enhanced SplitText Component for Syrian Identity
export interface SyrianSplitTextProps {
  /**
   * Text content - supports Arabic and English
   */
  text: string;
  
  /**
   * Split animation type
   * @default 'chars'
   */
  splitType?: 'chars' | 'words' | 'lines' | 'words,chars';
  
  /**
   * Animation style with Syrian cultural elements
   * @default 'cultural'
   */
  animationStyle?: 'default' | 'cultural' | 'calligraphy' | 'geometric' | 'damascus';
  
  /**
   * Animation timing based on Syrian musical rhythms
   * @default 'contemplative'
   */
  timing?: 'fast' | 'normal' | 'comfortable' | 'contemplative' | 'ceremonial';
  
  /**
   * Stagger delay between characters/words (ms)
   * @default 50
   */
  staggerDelay?: number;
  
  /**
   * Animation duration for each element
   * @default 0.8
   */
  duration?: number;
  
  /**
   * Scroll trigger threshold
   * @default 0.2
   */
  threshold?: number;
  
  /**
   * Direction for RTL support
   * @default 'auto'
   */
  direction?: 'ltr' | 'rtl' | 'auto';
  
  /**
   * Cultural color scheme
   * @default 'heritage'
   */
  colorScheme?: 'heritage' | 'flag' | 'forest' | 'wheat' | 'damascus';
  
  /**
   * Typography variant
   * @default 'body'
   */
  variant?: 'heading' | 'subheading' | 'body' | 'caption';
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Container styles
   */
  style?: React.CSSProperties;
  
  /**
   * Callback when animation completes
   */
  onAnimationComplete?: () => void;
  
  /**
   * Callback when animation starts
   */
  onAnimationStart?: () => void;
}

/**
 * Syrian Split Text Animation Component
 * 
 * Advanced text animation with cultural timing and RTL support.
 * 
 * @example
 * ```tsx
 * // Cultural Arabic text animation
 * <SyrianSplitText 
 *   text="سوريا حُرّة"
 *   animationStyle="cultural"
 *   timing="ceremonial"
 *   direction="rtl"
 *   colorScheme="flag"
 * />
 * 
 * // English text with Damascus style
 * <SyrianSplitText 
 *   text="Free Syria"
 *   animationStyle="damascus"
 *   timing="contemplative"
 *   direction="ltr"
 *   colorScheme="heritage"
 * />
 * ```
 */
export const SyrianSplitText: React.FC<SyrianSplitTextProps> = ({
  text,
  splitType = 'chars',
  animationStyle = 'cultural',
  timing = 'contemplative',
  staggerDelay = 50,
  duration = 0.8,
  threshold = 0.2,
  direction = 'auto',
  colorScheme = 'heritage',
  variant = 'body',
  className,
  style,
  onAnimationComplete,
  onAnimationStart
}) => {
  const { prefersReducedMotion } = useAnimation();
  const [isVisible, setIsVisible] = React.useState(false);
  const [hasAnimated, setHasAnimated] = React.useState(false);
  const elementRef = React.useRef<HTMLDivElement>(null);
  const observerRef = React.useRef<IntersectionObserver | null>(null);
  const splitId = React.useId();
  
  // Detect text direction
  const textDirection = React.useMemo(() => {
    if (direction !== 'auto') return direction;
    // Simple Arabic detection
    const arabicRegex = /[\u0600-\u06FF\u0750-\u077F]/;
    return arabicRegex.test(text) ? 'rtl' : 'ltr';
  }, [text, direction]);
  
  // Get cultural timing
  const getAnimationDuration = () => {
    const timingMap = {
      fast: 0.4,
      normal: 0.6,
      comfortable: 0.8,
      contemplative: 1.2,
      ceremonial: 1.8
    };
    return timingMap[timing];
  };
  
  // Get cultural colors
  const getCulturalColors = () => {
    const colorSchemes = {
      heritage: {
        primary: 'var(--sid-forest-600)',
        secondary: 'var(--sid-wheat-500)',
        accent: 'var(--sid-umber-500)'
      },
      flag: {
        primary: '#007A3D',
        secondary: '#FFFFFF',
        accent: '#CE1126'
      },
      forest: {
        primary: 'var(--sid-forest-700)',
        secondary: 'var(--sid-forest-400)',
        accent: 'var(--sid-forest-200)'
      },
      wheat: {
        primary: 'var(--sid-wheat-700)',
        secondary: 'var(--sid-wheat-400)',
        accent: 'var(--sid-wheat-200)'
      },
      damascus: {
        primary: 'var(--sid-charcoal-800)',
        secondary: 'var(--sid-charcoal-500)',
        accent: 'var(--sid-wheat-400)'
      }
    };
    return colorSchemes[colorScheme];
  };
  
  // Split text into characters or words
  const splitText = React.useMemo(() => {
    if (splitType === 'chars') {
      return text.split('').map((char, index) => ({
        content: char,
        index,
        isSpace: char === ' '
      }));
    } else if (splitType === 'words') {
      return text.split(' ').map((word, index) => ({
        content: word,
        index,
        isSpace: false
      }));
    }
    return [{ content: text, index: 0, isSpace: false }];
  }, [text, splitType]);
  
  // Intersection Observer for scroll trigger
  React.useEffect(() => {
    const element = elementRef.current;
    if (!element || prefersReducedMotion) return;
    
    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setIsVisible(true);
          setHasAnimated(true);
          onAnimationStart?.();
        }
      },
      {
        threshold,
        rootMargin: '0px'
      }
    );
    
    observerRef.current.observe(element);
    
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [threshold, hasAnimated, prefersReducedMotion, onAnimationStart]);
  
  // Animation completion handler
  React.useEffect(() => {
    if (isVisible && !prefersReducedMotion) {
      const totalDuration = (splitText.length * staggerDelay) + (getAnimationDuration() * 1000);
      const timer = setTimeout(() => {
        onAnimationComplete?.();
      }, totalDuration);
      
      return () => clearTimeout(timer);
    }
  }, [isVisible, splitText.length, staggerDelay, onAnimationComplete, prefersReducedMotion]);
  
  // Inject cultural animation keyframes
  React.useEffect(() => {
    if (typeof document === 'undefined' || prefersReducedMotion) return;
    
    const styleId = `sid-split-${animationStyle}-${splitId}`;
    if (document.getElementById(styleId)) return;
    
    const style = document.createElement('style');
    style.id = styleId;
    
    const colors = getCulturalColors();
    let keyframes = '';
    
    switch (animationStyle) {
      case 'cultural':
        keyframes = `
          @keyframes culturalReveal-${splitId} {
            0% { 
              opacity: 0; 
              transform: translateY(30px) scale(0.8); 
              color: ${colors.secondary}; 
            }
            50% { 
              opacity: 0.7; 
              transform: translateY(-5px) scale(1.05); 
              color: ${colors.primary}; 
            }
            100% { 
              opacity: 1; 
              transform: translateY(0) scale(1); 
              color: ${colors.primary}; 
            }
          }
        `;
        break;
        
      case 'calligraphy':
        keyframes = `
          @keyframes calligraphyFlow-${splitId} {
            0% { 
              opacity: 0; 
              transform: translateX(${textDirection === 'rtl' ? '20px' : '-20px'}) rotate(${textDirection === 'rtl' ? '-5deg' : '5deg'}); 
            }
            60% { 
              opacity: 0.8; 
              transform: translateX(${textDirection === 'rtl' ? '-3px' : '3px'}) rotate(${textDirection === 'rtl' ? '2deg' : '-2deg'}); 
            }
            100% { 
              opacity: 1; 
              transform: translateX(0) rotate(0deg); 
            }
          }
        `;
        break;
        
      case 'geometric':
        keyframes = `
          @keyframes geometricReveal-${splitId} {
            0% { 
              opacity: 0; 
              transform: scale(0) rotate(45deg); 
              filter: blur(3px); 
            }
            70% { 
              opacity: 0.9; 
              transform: scale(1.1) rotate(-5deg); 
              filter: blur(1px); 
            }
            100% { 
              opacity: 1; 
              transform: scale(1) rotate(0deg); 
              filter: blur(0px); 
            }
          }
        `;
        break;
        
      case 'damascus':
        keyframes = `
          @keyframes damascusReveal-${splitId} {
            0% { 
              opacity: 0; 
              transform: translateY(40px) scale(0.9); 
              text-shadow: 0 0 10px ${colors.accent}; 
            }
            30% { 
              opacity: 0.3; 
              transform: translateY(20px) scale(0.95); 
              text-shadow: 0 0 5px ${colors.accent}; 
            }
            100% { 
              opacity: 1; 
              transform: translateY(0) scale(1); 
              text-shadow: none; 
            }
          }
        `;
        break;
        
      default:
        keyframes = `
          @keyframes defaultReveal-${splitId} {
            0% { opacity: 0; transform: translateY(20px); }
            100% { opacity: 1; transform: translateY(0); }
          }
        `;
    }
    
    style.textContent = keyframes;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, [animationStyle, splitId, textDirection, colorScheme, prefersReducedMotion]);
  
  // Get typography styles
  const getTypographyStyles = (): React.CSSProperties => {
    const variantStyles = {
      heading: {
        fontSize: 'var(--sid-text-4xl)',
        fontWeight: 'var(--sid-font-bold)',
        lineHeight: 'var(--sid-leading-tight)'
      },
      subheading: {
        fontSize: 'var(--sid-text-2xl)',
        fontWeight: 'var(--sid-font-semibold)',
        lineHeight: 'var(--sid-leading-snug)'
      },
      body: {
        fontSize: 'var(--sid-text-lg)',
        fontWeight: 'var(--sid-font-normal)',
        lineHeight: 'var(--sid-leading-relaxed)'
      },
      caption: {
        fontSize: 'var(--sid-text-sm)',
        fontWeight: 'var(--sid-font-normal)',
        lineHeight: 'var(--sid-leading-normal)'
      }
    };
    
    return variantStyles[variant];
  };
  
  const containerStyle: React.CSSProperties = {
    direction: textDirection,
    fontFamily: textDirection === 'rtl' ? 'var(--sid-font-arabic)' : 'var(--sid-font-latin)',
    ...getTypographyStyles(),
    ...style
  };
  
  const getCharacterStyle = (index: number, isSpace: boolean): React.CSSProperties => {
    if (prefersReducedMotion) {
      return { opacity: 1 };
    }
    
    if (isSpace) {
      return { display: 'inline-block', width: '0.25em' };
    }
    
    const animationDelay = `${index * staggerDelay}ms`;
    const animationName = `${animationStyle}Reveal-${splitId}`;
    
    return {
      display: 'inline-block',
      opacity: isVisible ? 1 : 0,
      animation: isVisible ? 
        `${animationName} ${getAnimationDuration()}s var(--sid-ease-${animationStyle === 'calligraphy' ? 'calligraphy' : 'graceful'}) forwards` : 
        'none',
      animationDelay,
      willChange: isVisible ? 'transform, opacity' : 'auto',
    };
  };
  
  return (
    <div
      ref={elementRef}
      className={className}
      style={containerStyle}
    >
      {splitText.map((item, index) => (
        <span
          key={index}
          style={getCharacterStyle(index, item.isSpace)}
        >
          {item.content}
        </span>
      ))}
    </div>
  );
};

// Cultural Text Reveal Component
export interface CulturalTextRevealProps {
  /**
   * Text content with cultural significance
   */
  text: string;

  /**
   * Reveal pattern
   * @default 'wave'
   */
  pattern?: 'wave' | 'spiral' | 'bloom' | 'cascade' | 'damascus';

  /**
   * Cultural theme
   * @default 'heritage'
   */
  theme?: 'heritage' | 'freedom' | 'unity' | 'peace' | 'strength';

  /**
   * Animation intensity
   * @default 'normal'
   */
  intensity?: 'subtle' | 'normal' | 'dramatic';

  /**
   * Direction for RTL support
   * @default 'auto'
   */
  direction?: 'ltr' | 'rtl' | 'auto';

  /**
   * Additional CSS classes
   */
  className?: string;

  /**
   * Container styles
   */
  style?: React.CSSProperties;
}

/**
 * Cultural Text Reveal Component
 *
 * Reveals text with patterns inspired by Syrian cultural elements.
 *
 * @example
 * ```tsx
 * // Freedom theme with dramatic intensity
 * <CulturalTextReveal
 *   text="سوريا حُرّة - Free Syria"
 *   pattern="bloom"
 *   theme="freedom"
 *   intensity="dramatic"
 *   direction="auto"
 * />
 * ```
 */
export const CulturalTextReveal: React.FC<CulturalTextRevealProps> = ({
  text,
  pattern = 'wave',
  theme = 'heritage',
  intensity = 'normal',
  direction = 'auto',
  className,
  style
}) => {
  const { prefersReducedMotion } = useAnimation();
  const [isRevealed, setIsRevealed] = React.useState(false);
  const elementRef = React.useRef<HTMLDivElement>(null);
  const revealId = React.useId();

  // Detect text direction
  const textDirection = React.useMemo(() => {
    if (direction !== 'auto') return direction;
    const arabicRegex = /[\u0600-\u06FF\u0750-\u077F]/;
    return arabicRegex.test(text) ? 'rtl' : 'ltr';
  }, [text, direction]);

  // Get theme colors
  const getThemeColors = () => {
    const themes = {
      heritage: {
        primary: 'var(--sid-forest-600)',
        secondary: 'var(--sid-wheat-500)',
        background: 'linear-gradient(45deg, var(--sid-forest-100), var(--sid-wheat-100))'
      },
      freedom: {
        primary: '#007A3D',
        secondary: '#CE1126',
        background: 'linear-gradient(45deg, #007A3D, #FFFFFF, #CE1126)'
      },
      unity: {
        primary: 'var(--sid-charcoal-700)',
        secondary: 'var(--sid-wheat-600)',
        background: 'linear-gradient(45deg, var(--sid-charcoal-200), var(--sid-wheat-200))'
      },
      peace: {
        primary: 'var(--sid-forest-500)',
        secondary: 'var(--sid-forest-200)',
        background: 'linear-gradient(45deg, var(--sid-forest-50), var(--sid-forest-100))'
      },
      strength: {
        primary: 'var(--sid-umber-700)',
        secondary: 'var(--sid-charcoal-600)',
        background: 'linear-gradient(45deg, var(--sid-umber-100), var(--sid-charcoal-100))'
      }
    };
    return themes[theme];
  };

  // Intersection Observer
  React.useEffect(() => {
    const element = elementRef.current;
    if (!element || prefersReducedMotion) {
      setIsRevealed(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsRevealed(true);
        }
      },
      { threshold: 0.3 }
    );

    observer.observe(element);

    return () => observer.disconnect();
  }, [prefersReducedMotion]);

  // Inject pattern keyframes
  React.useEffect(() => {
    if (typeof document === 'undefined' || prefersReducedMotion) return;

    const styleId = `sid-reveal-${pattern}-${revealId}`;
    if (document.getElementById(styleId)) return;

    const style = document.createElement('style');
    style.id = styleId;

    const colors = getThemeColors();
    const intensityMultiplier = intensity === 'subtle' ? 0.5 : intensity === 'dramatic' ? 1.5 : 1;

    let keyframes = '';
    switch (pattern) {
      case 'wave':
        keyframes = `
          @keyframes waveReveal-${revealId} {
            0% {
              opacity: 0;
              transform: translateY(${30 * intensityMultiplier}px) scale(${0.8 + (0.1 * intensityMultiplier)});
              background: ${colors.background};
              background-size: 200% 200%;
              background-position: 0% 50%;
            }
            50% {
              opacity: 0.7;
              transform: translateY(${-10 * intensityMultiplier}px) scale(${1.05 * intensityMultiplier});
              background-position: 100% 50%;
            }
            100% {
              opacity: 1;
              transform: translateY(0) scale(1);
              background-position: 50% 50%;
            }
          }
        `;
        break;

      case 'bloom':
        keyframes = `
          @keyframes bloomReveal-${revealId} {
            0% {
              opacity: 0;
              transform: scale(0.3) rotate(${-10 * intensityMultiplier}deg);
              filter: blur(${5 * intensityMultiplier}px);
              color: ${colors.secondary};
            }
            60% {
              opacity: 0.8;
              transform: scale(${1.1 * intensityMultiplier}) rotate(${5 * intensityMultiplier}deg);
              filter: blur(${1 * intensityMultiplier}px);
              color: ${colors.primary};
            }
            100% {
              opacity: 1;
              transform: scale(1) rotate(0deg);
              filter: blur(0px);
              color: ${colors.primary};
            }
          }
        `;
        break;

      case 'damascus':
        keyframes = `
          @keyframes damascusReveal-${revealId} {
            0% {
              opacity: 0;
              transform: translateY(${50 * intensityMultiplier}px) scale(0.9);
              text-shadow: 0 0 ${20 * intensityMultiplier}px ${colors.secondary};
              letter-spacing: ${0.2 * intensityMultiplier}em;
            }
            40% {
              opacity: 0.6;
              transform: translateY(${20 * intensityMultiplier}px) scale(0.95);
              text-shadow: 0 0 ${10 * intensityMultiplier}px ${colors.secondary};
              letter-spacing: ${0.1 * intensityMultiplier}em;
            }
            100% {
              opacity: 1;
              transform: translateY(0) scale(1);
              text-shadow: none;
              letter-spacing: normal;
            }
          }
        `;
        break;

      default:
        keyframes = `
          @keyframes defaultReveal-${revealId} {
            0% { opacity: 0; transform: translateY(20px); }
            100% { opacity: 1; transform: translateY(0); }
          }
        `;
    }

    style.textContent = keyframes;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, [pattern, revealId, theme, intensity, prefersReducedMotion]);

  const containerStyle: React.CSSProperties = {
    direction: textDirection,
    fontFamily: textDirection === 'rtl' ? 'var(--sid-font-arabic)' : 'var(--sid-font-latin)',
    fontSize: 'var(--sid-text-2xl)',
    fontWeight: 'var(--sid-font-bold)',
    textAlign: 'center',
    padding: '2rem',
    borderRadius: 'var(--sid-radius-lg)',
    opacity: prefersReducedMotion ? 1 : (isRevealed ? 1 : 0),
    animation: prefersReducedMotion ? 'none' :
      isRevealed ? `${pattern}Reveal-${revealId} var(--sid-duration-ceremonial) var(--sid-ease-graceful) forwards` : 'none',
    ...style
  };

  return (
    <div
      ref={elementRef}
      className={className}
      style={containerStyle}
    >
      {text}
    </div>
  );
};
